import java.util.List;

public class App {
    public static void main(String[] args) throws Exception {
        Extractor extractor = new Extractor();
        List<String> data = extractor.readDataFromFile("lib/data.txt");
        
        int[] coordinates = findCoordinates(data);
        System.out.println(data.get(1));
    }

    public static int[] findCoordinates(List<String> data) {
        int[] coordinates = new int[2];
        for (int i = 0; i < data.size(); i++) {
            for (int j = 0; j < data.get(i).length(); j++) {
                if (data.get(i).charAt(j) == '^') {
                    coordinates[0] = i;
                    coordinates[1] = j;
                    return coordinates;
                }
            }

        }
        return coordinates;
    }

}
